#!/bin/bash

# Remnawave Backup and Restore Functionality

BACKUP_INSTALL_DIR="/opt/rw-backup-restore"
BACKUP_DIR="$BACKUP_INSTALL_DIR/backup"
BACKUP_CONFIG_FILE="$BAC<PERSON>UP_INSTALL_DIR/config.env"
BACKUP_SCRIPT_NAME="rw-backup.sh"
BACKUP_SCRIPT_PATH="$BACKUP_INSTALL_DIR/$BACKUP_SCRIPT_NAME"
BACKUP_LOG_FILE="$BACKUP_INSTALL_DIR/backup.log"
RETAIN_BACKUPS_DAYS=7
SYMLINK_PATH="/usr/local/bin/rw-backup"

# File paths for .env files
ENV_NODE_FILE=".env-node"
ENV_FILE=".env"
UPLOAD_METHOD="telegram"

# Initialize variables that will be loaded from config
BOT_TOKEN=""
CHAT_ID=""
DB_USER="postgres"
GD_CLIENT_ID=""
GD_CLIENT_SECRET=""
GD_REFRESH_TOKEN=""
GD_FOLDER_ID=""
CRON_TIMES=""

# Backup validation constants
BACKUP_MAGIC_HEADER="REMNAWAVE_BACKUP_V1"
BACKUP_REQUIRED_FILES=("database.sql" "env/.env")
BACKUP_CHECKSUM_FILE="backup.sha256"

# Parse command line arguments for backup script
BACKUP_CREATE_MODE=false
BACKUP_SILENT_MODE=false

# Only parse arguments if this script is being run directly (not sourced)
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    while [[ $# -gt 0 ]]; do
        case $1 in
            --create)
                BACKUP_CREATE_MODE=true
                shift
                ;;
            --silent)
                BACKUP_SILENT_MODE=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
fi

# Universal error handling function
handle_backup_error() {
    local error_message="$1"
    local cleanup_function="${2:-}"
    local return_code="${3:-1}"

    show_error "$error_message"

    # Execute cleanup function if provided
    if [[ -n "$cleanup_function" && "$(type -t "$cleanup_function")" == "function" ]]; then
        "$cleanup_function"
    fi

    # Log error if logging is enabled
    log_backup_operation "ERROR" "$error_message"

    prompt_for_enter
    return "$return_code"
}

# Logging function for backup operations
log_backup_operation() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$BACKUP_LOG_FILE")"

    # Write log entry
    echo "[$timestamp] [$level] $message" >> "$BACKUP_LOG_FILE"
}

# Main menu for backup and restore functionality
backup_restore_menu() {
    while true; do
        clear_screen
        draw_section_header "$(t backup_menu_title)"

        echo -e "${GREEN}1.${NC} $(t backup_menu_create)"
        echo -e "${GREEN}2.${NC} $(t backup_menu_restore)"
        echo -e "${GREEN}3.${NC} $(t backup_menu_configure)"
        echo -e "${GREEN}4.${NC} $(t backup_menu_schedule)"
        echo
        echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
        echo
        echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
        read choice
        echo

        case $choice in
            1) create_backup ;;
            2) restore_backup ;;
            3) load_or_create_config ;;
            4) setup_auto_send ;;
            0) return ;;
            *)
                clear_screen
                echo -e "${BOLD_RED}$(t error_invalid_choice)${NC}"
                sleep 1
                ;;
        esac
    done
}

# Validate Telegram configuration
validate_telegram_config() {
    local bot_token="$1"
    local chat_id="$2"

    # Check if bot token is provided and has correct format
    if [[ -z "$bot_token" ]]; then
        show_error "Telegram bot token is required"
        return 1
    fi

    # Basic bot token format validation (should be like 123456789:ABC-DEF...)
    if [[ ! "$bot_token" =~ ^[0-9]+:[A-Za-z0-9_-]+$ ]]; then
        show_error "Invalid Telegram bot token format"
        return 1
    fi

    # Check if chat ID is provided
    if [[ -z "$chat_id" ]]; then
        show_error "Telegram chat ID is required"
        return 1
    fi

    # Chat ID should be numeric (can be negative for groups)
    if [[ ! "$chat_id" =~ ^-?[0-9]+$ ]]; then
        show_error "Invalid Telegram chat ID format"
        return 1
    fi

    # Test connection to Telegram API
    show_info "Testing Telegram connection..."
    local test_response=$(curl -s -X POST "https://api.telegram.org/bot$bot_token/getMe" 2>/dev/null)

    if [[ -z "$test_response" ]]; then
        show_error "Failed to connect to Telegram API"
        return 1
    fi

    # Check if the response indicates success
    if ! echo "$test_response" | jq -e '.ok == true' >/dev/null 2>&1; then
        show_error "Invalid Telegram bot token"
        return 1
    fi

    show_success "Telegram configuration is valid"
    return 0
}

# Validate Google Drive configuration
validate_gdrive_config() {
    local client_id="$1"
    local client_secret="$2"

    # Check if client ID is provided
    if [[ -z "$client_id" ]]; then
        show_error "Google Drive Client ID is required"
        return 1
    fi

    # Check if client secret is provided
    if [[ -z "$client_secret" ]]; then
        show_error "Google Drive Client Secret is required"
        return 1
    fi

    # Basic format validation for Google OAuth2 client ID
    if [[ ! "$client_id" =~ ^[0-9]+-[a-zA-Z0-9]+\.apps\.googleusercontent\.com$ ]]; then
        show_error "Invalid Google Drive Client ID format"
        return 1
    fi

    show_success "Google Drive configuration format is valid"
    return 0
}

# Function to send notification messages via Telegram
send_telegram_message() {
    local message="$1"
    local parse_mode="${2:-MarkdownV2}"
    local escaped_message

    if [[ "$parse_mode" == "MarkdownV2" ]]; then
        escaped_message=$(escape_markdown_v2 "$message")
    else
        escaped_message="$message"
    fi

    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        handle_backup_error "$(t backup_config_telegram_bot) $(t backup_config_telegram_id)"
        return 1
    fi

    local http_code=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
        -d chat_id="$CHAT_ID" \
        -d text="$escaped_message" \
        -d parse_mode="$parse_mode" \
        -w "%{http_code}" -o /dev/null 2>&1)

    if [[ "$http_code" -eq 200 ]]; then
        return 0
    else
        handle_backup_error "Failed to send Telegram message. HTTP code: $http_code"
        return 1
    fi
}

# Function to escape special characters for Telegram MarkdownV2 format
escape_markdown_v2() {
    local text="$1"
    echo "$text" | sed \
        -e 's/\\/\\\\/g' \
        -e 's/_/\\_/g' \
        -e 's/\[/\\[/g' \
        -e 's/\]/\\]/g' \
        -e 's/(/\\(/g' \
        -e 's/)/\\)/g' \
        -e 's/~/\\~/g' \
        -e 's/`/\\`/g' \
        -e 's/>/\\>/g' \
        -e 's/#/\\#/g' \
        -e 's/+/\\+/g' \
        -e 's/-/\\-/g' \
        -e 's/=/\\=/g' \
        -e 's/|/\\|/g' \
        -e 's/{/\\{/g' \
        -e 's/}/\\}/g' \
        -e 's/\./\\./g' \
        -e 's/!/\\!/g'
}

# Function to send files via Telegram
send_telegram_document() {
    local file_path="$1"
    local caption="$2"
    local parse_mode="MarkdownV2"
    local escaped_caption
    escaped_caption=$(escape_markdown_v2 "$caption")

    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        handle_backup_error "$(t backup_config_telegram_bot) $(t backup_config_telegram_id)"
        return 1
    fi

    local http_code=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendDocument" \
        -F chat_id="$CHAT_ID" \
        -F document=@"$file_path" \
        -F parse_mode="$parse_mode" \
        -F caption="$escaped_caption" \
        -w "%{http_code}" -o /dev/null 2>&1)

    if [[ "$http_code" == "200" ]]; then
        return 0
    else
        handle_backup_error "Failed to send document to Telegram. HTTP code: $http_code"
        return 1
    fi
}

# Function to validate backup integrity
validate_backup_integrity() {
    local backup_path="$1"
    local temp_dir="$2"

    log_backup_operation "INFO" "Validating backup integrity: $(basename "$backup_path")"

    # Check if backup file exists
    if [[ ! -f "$backup_path" ]]; then
        handle_backup_error "Backup file does not exist: $backup_path"
        return 1
    fi

    # Check if backup file is not empty
    if [[ ! -s "$backup_path" ]]; then
        handle_backup_error "Backup file is empty: $backup_path"
        return 1
    fi

    # Test if archive can be extracted
    tar -tzf "$backup_path" >/dev/null 2>&1 &
    spinner $! "$(t backup_spinner_testing_archive)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Backup archive is corrupted or invalid"
        return 1
    fi

    # Extract to temporary directory for validation
    local validation_dir=$(mktemp -d)
    if ! tar -xzf "$backup_path" -C "$validation_dir" >/dev/null 2>&1; then
        rm -rf "$validation_dir"
        handle_backup_error "Failed to extract backup for validation"
        return 1
    fi

    # Check for required files
    local missing_files=()
    for required_file in "${BACKUP_REQUIRED_FILES[@]}"; do
        if [[ ! -f "$validation_dir/$required_file" ]]; then
            missing_files+=("$required_file")
        fi
    done

    if [[ ${#missing_files[@]} -gt 0 ]]; then
        rm -rf "$validation_dir"
        handle_backup_error "Backup is missing required files: ${missing_files[*]}"
        return 1
    fi

    # Validate database dump
    if [[ -f "$validation_dir/database.sql" ]]; then
        if [[ ! -s "$validation_dir/database.sql" ]]; then
            rm -rf "$validation_dir"
            handle_backup_error "Database dump is empty"
            return 1
        fi

        # Basic SQL validation - check for common PostgreSQL dump patterns
        if ! grep -q "PostgreSQL database dump" "$validation_dir/database.sql" 2>/dev/null; then
            show_warning "Database dump may not be a valid PostgreSQL dump"
        fi
    fi

    # Validate checksum if present
    if [[ -f "$validation_dir/$BACKUP_CHECKSUM_FILE" ]]; then
        (
            cd "$validation_dir"
            sha256sum -c "$BACKUP_CHECKSUM_FILE" >/dev/null 2>&1
            local result=$?
            cd - >/dev/null
            exit $result
        ) &
        spinner $! "$(t backup_spinner_verifying_checksum)"
        wait $!

        if [[ $? -ne 0 ]]; then
            rm -rf "$validation_dir"
            handle_backup_error "Backup checksum verification failed"
            return 1
        fi
        show_success "Backup checksum verified"
    fi

    rm -rf "$validation_dir"

    log_backup_operation "INFO" "Backup integrity validation successful"
    return 0
}

# Function to create necessary directories
create_directories() {
    if ! mkdir -p "$BACKUP_INSTALL_DIR" 2>/dev/null; then
        handle_backup_error "Failed to create backup install directory: $BACKUP_INSTALL_DIR"
        return 1
    fi

    if ! mkdir -p "$BACKUP_DIR" 2>/dev/null; then
        handle_backup_error "Failed to create backup directory: $BACKUP_DIR"
        return 1
    fi

    # Only change ownership if running as root and USER is defined
    if [[ $EUID -eq 0 && -n "$USER" && "$USER" != "root" ]]; then
        chown -R "$USER:$USER" "$BACKUP_INSTALL_DIR" 2>/dev/null || true
    fi

    return 0
}

# Function to cleanup temporary files
cleanup_temp_files() {
    local temp_dir="$1"
    if [[ -n "$temp_dir" && -d "$temp_dir" ]]; then
        rm -rf "$temp_dir"
        log_backup_operation "INFO" "Cleaned up temporary directory: $temp_dir"
    fi
}

# Session flag for dependency validation
BACKUP_DEPS_VALIDATED=""

# Function to initialize the backup system
initialize_backup() {
    log_backup_operation "INFO" "Initializing backup system"

    local actions_performed=false

    # Create required directories only if they don't exist
    if [[ ! -d "$BACKUP_INSTALL_DIR" ]] || [[ ! -d "$BACKUP_DIR" ]]; then
        spinner $$ "Creating directories..." &
        local spinner_pid=$!
        if ! create_directories; then
            kill $spinner_pid 2>/dev/null
            printf "\r\033[K" >/dev/tty
            return 1
        fi
        kill $spinner_pid 2>/dev/null
        printf "\r\033[K" >/dev/tty
        show_success "Directories created"
        actions_performed=true
    fi

    # Create symlink only if it doesn't exist or points to wrong target
    if [[ ! -L "$SYMLINK_PATH" ]] || [[ "$(readlink "$SYMLINK_PATH" 2>/dev/null)" != "$BACKUP_SCRIPT_PATH" ]]; then
        spinner $$ "Creating symlink..." &
        local spinner_pid=$!
        ln -sf "$BACKUP_SCRIPT_PATH" "$SYMLINK_PATH" 2>/dev/null || true
        kill $spinner_pid 2>/dev/null
        printf "\r\033[K" >/dev/tty
        show_success "Symlink created"
        actions_performed=true
    fi

    # Skip dependency validation if already verified in current session
    if [[ "$BACKUP_DEPS_VALIDATED" != "true" ]]; then
        # Ensure Docker is installed and running
        if ! command -v docker &>/dev/null; then
            handle_backup_error "Docker not installed. Please install Docker first."
            return 1
        fi

        # Check if required tools are available
        local missing_tools=()
        for cmd in curl gzip tar jq sha256sum; do
            if ! command -v $cmd &>/dev/null; then
                missing_tools+=("$cmd")
            fi
        done

        if [[ ${#missing_tools[@]} -gt 0 ]]; then
            handle_backup_error "Required commands not found: ${missing_tools[*]}. Please install them."
            return 1
        fi

        BACKUP_DEPS_VALIDATED="true"
        actions_performed=true
    fi

    if [[ "$actions_performed" == "true" ]]; then
        show_success "Backup system initialized successfully"
    fi

    log_backup_operation "INFO" "Backup system initialized successfully"
    return 0
}

# Function to save configuration
save_config() {
    # Create the config directory if it doesn't exist
    mkdir -p "$(dirname "$BACKUP_CONFIG_FILE")" 2>/dev/null
    
    # Write config values to file
    cat > "$BACKUP_CONFIG_FILE" << EOF
BOT_TOKEN="$BOT_TOKEN"
CHAT_ID="$CHAT_ID"
DB_USER="$DB_USER"
UPLOAD_METHOD="$UPLOAD_METHOD"
GD_CLIENT_ID="$GD_CLIENT_ID"
GD_CLIENT_SECRET="$GD_CLIENT_SECRET"
GD_REFRESH_TOKEN="$GD_REFRESH_TOKEN"
GD_FOLDER_ID="$GD_FOLDER_ID"
CRON_TIMES="$CRON_TIMES"
RETAIN_BACKUPS_DAYS="$RETAIN_BACKUPS_DAYS"
EOF
    
    # Set appropriate permissions
    chmod 600 "$BACKUP_CONFIG_FILE"
    
    show_info "$(t backup_config_saved)"
}

# Function to load configuration
load_config() {
    if [[ -f "$BACKUP_CONFIG_FILE" ]]; then
        # Source the config file
        # shellcheck disable=SC1090
        source "$BACKUP_CONFIG_FILE"
        return 0
    else
        return 1
    fi
}

# Function to prompt for configuration settings
prompt_for_config() {
    clear_screen
    draw_section_header "$(t backup_config_method)"

    echo -e "${GREEN}1.${NC} $(t backup_config_telegram)"
    echo -e "${GREEN}2.${NC} $(t backup_config_google)"
    echo
    echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
    echo
    echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
    read choice
    echo

    # Validate choice
    if ! [[ "$choice" =~ ^[0-2]$ ]]; then
        handle_backup_error "$(t error_invalid_choice)"
        return 1
    fi

    # Handle back option
    if [[ "$choice" -eq 0 ]]; then
        return 0
    fi

    if [[ "$choice" == "1" ]]; then
        UPLOAD_METHOD="telegram"
        clear_screen

        draw_section_header "$(t backup_config_telegram)"

        echo -e "$(t backup_config_telegram_bot):"
        read -r BOT_TOKEN

        echo -e "$(t backup_config_telegram_id):"
        read -r CHAT_ID

        # Validate Telegram configuration
        if ! validate_telegram_config "$BOT_TOKEN" "$CHAT_ID"; then
            return 1
        fi
    else
        UPLOAD_METHOD="gdrive"
        clear_screen

        draw_section_header "$(t backup_config_google)"

        echo -e "Google Drive Client ID:"
        read -r GD_CLIENT_ID

        echo -e "Google Drive Client Secret:"
        read -r GD_CLIENT_SECRET

        echo -e "Google Drive Refresh Token (optional):"
        read -r GD_REFRESH_TOKEN

        echo -e "Google Drive Folder ID (optional):"
        read -r GD_FOLDER_ID

        # Validate Google Drive configuration
        if ! validate_gdrive_config "$GD_CLIENT_ID" "$GD_CLIENT_SECRET"; then
            return 1
        fi
    fi

    save_config
    log_backup_operation "INFO" "Configuration updated for method: $UPLOAD_METHOD"
}

# Function to load or create configuration
load_or_create_config() {
    if ! load_config; then
        prompt_for_config
    else
        prompt_for_config
    fi
}

# Function to create a backup
create_backup() {
    clear_screen
    draw_section_header "Creating backup"

    log_backup_operation "INFO" "Starting backup creation"

    # Initialize backup
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system" "cleanup_temp_files"
        return 1
    fi

    # Load configuration if exists
    load_config

    # Try to get database user from Remnawave .env file if not set in config
    if [[ "$DB_USER" == "postgres" && -f "$REMNAWAVE_DIR/.env" ]]; then
        local env_db_user=$(grep "^POSTGRES_USER=" "$REMNAWAVE_DIR/.env" | cut -d'=' -f2 | tr -d '"')
        if [[ -n "$env_db_user" ]]; then
            DB_USER="$env_db_user"
            show_info "Using database user from .env: $DB_USER"
        fi
    fi

    # Generate timestamp for backup file
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="remnawave_backup_${timestamp}.tar.gz"
    local backup_path="$BACKUP_DIR/$backup_file"

    # Create temporary directory
    local temp_dir=$(mktemp -d)
    local db_dump="$temp_dir/database.sql"
    local env_dir="$temp_dir/env"
    local checksum_file="$temp_dir/$BACKUP_CHECKSUM_FILE"

    # Create directory for env files
    if ! mkdir -p "$env_dir"; then
        handle_backup_error "Failed to create temporary environment directory" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Check if remnawave-db container exists and is running
    if ! docker ps --format "{{.Names}}" | grep -q "^remnawave-db$"; then
        handle_backup_error "Remnawave database container is not running. Please make sure Remnawave panel is installed and running" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Export PostgreSQL database from Docker container
    docker exec remnawave-db pg_dump -U "$DB_USER" -d remnawave_db > "$db_dump" 2>/dev/null &
    spinner $! "$(t backup_spinner_exporting_db)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Failed to export database. Check if database user '$DB_USER' exists" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Verify database dump is not empty
    if [[ ! -s "$db_dump" ]]; then
        handle_backup_error "Database dump is empty" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Find and copy .env files
    (
        # Check for panel .env file and copy if exists
        if [[ -f "$REMNAWAVE_DIR/$ENV_FILE" ]]; then
            if ! cp "$REMNAWAVE_DIR/$ENV_FILE" "$env_dir/" 2>/dev/null; then
                echo "Warning: Failed to copy panel .env file" >&2
            fi
        fi

        # Check for node .env file and copy if exists
        if [[ -f "$REMNAWAVE_DIR/$ENV_NODE_FILE" ]]; then
            if ! cp "$REMNAWAVE_DIR/$ENV_NODE_FILE" "$env_dir/" 2>/dev/null; then
                echo "Warning: Failed to copy node .env file" >&2
            fi
        fi
    ) &
    spinner $! "$(t backup_spinner_copying_env)"
    wait $!

    # Create backup metadata
    echo "$BACKUP_MAGIC_HEADER" > "$temp_dir/backup.info"
    echo "Created: $(date '+%Y-%m-%d %H:%M:%S')" >> "$temp_dir/backup.info"
    echo "Server: $(hostname)" >> "$temp_dir/backup.info"
    echo "Version: $VERSION" >> "$temp_dir/backup.info"

    # Generate checksums for validation
    (
        cd "$temp_dir"
        find . -type f -not -name "$BACKUP_CHECKSUM_FILE" -exec sha256sum {} \; > "$checksum_file"
        cd - >/dev/null
    ) &
    spinner $! "$(t backup_spinner_generating_checksums)"
    wait $!

    # Create archive of all backup files
    tar -czf "$backup_path" -C "$temp_dir" . >/dev/null 2>&1 &
    spinner $! "$(t backup_spinner_creating_archive)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Failed to create backup archive" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Validate the created backup
    validate_backup_integrity "$backup_path" "$temp_dir" &
    spinner $! "$(t backup_spinner_validating_integrity)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Backup validation failed" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Clean up temporary directory
    cleanup_temp_files "$temp_dir"

    # Clean up old backups (older than specified days)
    find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -mtime +"$RETAIN_BACKUPS_DAYS" -delete 2>/dev/null

    # Send backup to the configured method if available
    if [[ "$UPLOAD_METHOD" == "telegram" && -n "$BOT_TOKEN" && -n "$CHAT_ID" ]]; then
        local server_name=$(hostname)
        local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")
        local caption="Remnawave Backup\nServer: $server_name\nIP: $server_ip\nDate: $(date '+%Y-%m-%d %H:%M:%S')"

        (send_telegram_document "$backup_path" "$caption") &
        spinner $! "$(t backup_spinner_sending_telegram)"
        wait $!

        if [[ $? -eq 0 ]]; then
            show_success "Backup sent via Telegram"
            log_backup_operation "INFO" "Backup sent via Telegram successfully"
        else
            show_error "Failed to send backup via Telegram"
            log_backup_operation "ERROR" "Failed to send backup via Telegram"
        fi
    elif [[ "$UPLOAD_METHOD" == "gdrive" && -n "$GD_CLIENT_ID" && -n "$GD_CLIENT_SECRET" ]]; then
        show_info "Google Drive upload not yet implemented"
        log_backup_operation "INFO" "Google Drive upload requested but not implemented"
    fi

    show_success "$(t backup_success): $backup_path"
    log_backup_operation "INFO" "Backup created successfully: $backup_path"
    prompt_for_enter
    return 0
}

# Function to show detailed backup information
show_backup_info() {
    local backup_path="$1"
    local temp_dir=$(mktemp -d)

    # Extract backup for inspection
    if ! tar -xzf "$backup_path" -C "$temp_dir" >/dev/null 2>&1; then
        show_error "Failed to extract backup for inspection"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    echo
    echo -e "${BOLD_GREEN}Backup Information:${NC}"
    echo -e "${ORANGE}File:${NC} $(basename "$backup_path")"
    echo -e "${ORANGE}Size:${NC} $(du -h "$backup_path" | cut -f1)"
    echo -e "${ORANGE}Created:${NC} $(date -r "$backup_path" "+%Y-%m-%d %H:%M:%S")"

    # Show backup metadata if available
    if [[ -f "$temp_dir/backup.info" ]]; then
        echo -e "${ORANGE}Metadata:${NC}"
        while IFS= read -r line; do
            echo "  $line"
        done < "$temp_dir/backup.info"
    fi

    # Show database size
    if [[ -f "$temp_dir/database.sql" ]]; then
        local db_size=$(du -h "$temp_dir/database.sql" | cut -f1)
        echo -e "${ORANGE}Database dump size:${NC} $db_size"
    fi

    # Show environment files
    if [[ -d "$temp_dir/env" ]]; then
        echo -e "${ORANGE}Environment files:${NC}"
        find "$temp_dir/env" -type f -exec basename {} \; | sed 's/^/  /'
    fi

    cleanup_temp_files "$temp_dir"
}

# Function to get backup files array (newest to oldest)
get_backup_files() {
    local backups=()
    local i=0

    # Check if backup directory exists
    if [[ ! -d "$BACKUP_DIR" ]]; then
        return 1
    fi

    # Find and list all backup files (sorted newest to oldest)
    while IFS= read -r file; do
        backups[i++]="$file"
    done < <(find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -printf "%T@ %p\n" | sort -nr | cut -d' ' -f2-)

    # Return 1 if no backups found
    if [[ "${#backups[@]}" -eq 0 ]]; then
        return 1
    fi

    # Return the array of backup files
    echo "${backups[@]}"
}

# Function to display backup list with enhanced information
display_backup_list() {
    local backups=("$@")

    echo -e "${BOLD_GREEN}Available backups:${NC}"
    echo
    for i in "${!backups[@]}"; do
        local file="${backups[$i]}"
        local date=$(date -r "$file" "+%Y-%m-%d %H:%M:%S")
        local size=$(du -h "$file" | cut -f1)
        local filename=$(basename "$file")

        # All backups show as valid (no integrity check)
        local status_icon="✓"
        local status_color="$GREEN"

        # Display backup in single line format
        echo -e "${GREEN}$((i+1)).${NC} ${status_color}${status_icon}${NC} $filename ${ORANGE}[Date: $date | Size: $size]${NC}"
    done
}

# Function to list available backups with enhanced display (legacy compatibility)
list_backups() {
    local backup_files_output
    backup_files_output=$(get_backup_files)

    if [[ $? -ne 0 ]]; then
        return 1
    fi

    local backups=($backup_files_output)
    display_backup_list "${backups[@]}"

    # Return the array of backup files for compatibility
    echo "${backups[@]}"
}

# Function to restore from a backup
restore_backup() {
    clear_screen
    draw_section_header "Restoring from backup"

    log_backup_operation "INFO" "Starting backup restoration"

    # Initialize backup system
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system"
        return 1
    fi

    # Get array of available backup files
    local backup_files_output
    backup_files_output=$(get_backup_files)

    if [[ $? -ne 0 ]]; then
        handle_backup_error "$(t backup_no_backups)"
        return 1
    fi

    # Convert to array properly
    local backups
    IFS=' ' read -ra backups <<< "$backup_files_output"
    local backup_count=${#backups[@]}

    # Display the numbered backup list
    echo -e "${BOLD_GREEN}Available backups:${NC}"
    echo
    for i in "${!backups[@]}"; do
        local file="${backups[$i]}"
        local date=$(date -r "$file" "+%Y-%m-%d %H:%M:%S")
        local size=$(du -h "$file" | cut -f1)
        local filename=$(basename "$file")

        # All backups show as valid (no integrity check)
        local status_icon="✓"
        local status_color="$GREEN"

        # Display backup in single line format
        echo -e "${GREEN}$((i+1)).${NC} ${status_color}${status_icon}${NC} $filename ${ORANGE}[Date: $date | Size: $size]${NC}"
    done

    # Backup selection menu (numeric only)
    echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
    echo
    echo -ne "${BOLD_BLUE_MENU}Select backup number (1-$backup_count) or 0 to go back: ${NC}"
    read choice
    echo

    # Validate choice
    if ! [[ "$choice" =~ ^[0-9]+$ ]] || [ "$choice" -lt 0 ] || [ "$choice" -gt "$backup_count" ]; then
        handle_backup_error "$(t error_invalid_choice)"
        return 1
    fi

    if [[ "$choice" -eq 0 ]]; then
        return 0
    fi

    local selected_backup="${backups[$((choice-1))]}"

    # Show selected backup information
    echo -e "${BOLD_GREEN}Selected backup:${NC}"
    show_backup_info "$selected_backup"
    echo



    # Warning before restore
    clear_screen
    draw_section_header "WARNING"
    echo -e "${BOLD_RED}This will restore the database and configuration files.${NC}"
    echo -e "${YELLOW}All current data will be replaced with data from the backup.${NC}"
    echo -e "${YELLOW}The panel will be restarted during restoration.${NC}"
    echo
    echo -e "${ORANGE}Selected backup:${NC} $(basename "$selected_backup")"
    echo -e "${ORANGE}Backup date:${NC} $(date -r "$selected_backup" "+%Y-%m-%d %H:%M:%S")"

    echo ""
    if ! prompt_yes_no "Continue with restore?" "$BOLD_RED"; then
        log_backup_operation "INFO" "Backup restoration cancelled by user"
        return 0
    fi

    # Create temporary directory for extraction
    local temp_dir=$(mktemp -d)

    # Extract the backup archive
    show_info "Extracting backup..."
    if ! tar -xzf "$selected_backup" -C "$temp_dir" >/dev/null 2>&1; then
        handle_backup_error "Failed to extract backup archive" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Validate backup contents
    if [[ ! -f "$temp_dir/database.sql" ]]; then
        handle_backup_error "Invalid backup: Missing database dump" "cleanup_temp_files $temp_dir"
        return 1
    fi



    # Load configuration to get DB_USER
    load_config

    # Try to get database user from Remnawave .env file if not set in config
    if [[ "$DB_USER" == "postgres" && -f "$REMNAWAVE_DIR/.env" ]]; then
        local env_db_user=$(grep "^POSTGRES_USER=" "$REMNAWAVE_DIR/.env" | cut -d'=' -f2 | tr -d '"')
        if [[ -n "$env_db_user" ]]; then
            DB_USER="$env_db_user"
        fi
    fi

    # Stop panel services
    show_info "Stopping Remnawave services..."
    if ! docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" down >/dev/null 2>&1; then
        show_warning "Failed to stop some services, continuing with restoration"
    fi

    # Start only database service
    show_info "Starting database service..."
    if ! docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d remnawave-db >/dev/null 2>&1; then
        handle_backup_error "Failed to start database container" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Wait for database to be ready with active monitoring
    show_info "Waiting for database to be ready..."
    local max_attempts=30
    local attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if docker exec remnawave-db pg_isready -U "$DB_USER" >/dev/null 2>&1; then
            break
        fi
        sleep 1
        attempt=$((attempt + 1))

        # Show progress every 5 attempts
        if [ $((attempt % 5)) -eq 0 ]; then
            show_info "Still waiting for database... (attempt $attempt/$max_attempts)"
        fi
    done

    if [ $attempt -eq $max_attempts ]; then
        handle_backup_error "Database failed to start within expected time" "cleanup_temp_files $temp_dir"
        # Try to restart services before returning
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
        return 1
    fi

    # Restore database with error handling
    show_info "Importing database dump..."
    if ! docker exec -i remnawave-db psql -U "$DB_USER" -d remnawave_db < "$temp_dir/database.sql" >/dev/null 2>&1; then
        handle_backup_error "Failed to restore database" "cleanup_temp_files $temp_dir"
        # Try to restart services before returning
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
        return 1
    fi

    # Restore env files if they exist in the backup
    if [[ -d "$temp_dir/env" ]]; then
        show_info "Restoring environment files..."

        # Backup current env files before restoration
        local backup_suffix=$(date +"%Y%m%d_%H%M%S")
        if [[ -f "$REMNAWAVE_DIR/$ENV_FILE" ]]; then
            cp "$REMNAWAVE_DIR/$ENV_FILE" "$REMNAWAVE_DIR/${ENV_FILE}.backup_${backup_suffix}" 2>/dev/null
        fi
        if [[ -f "$REMNAWAVE_DIR/$ENV_NODE_FILE" ]]; then
            cp "$REMNAWAVE_DIR/$ENV_NODE_FILE" "$REMNAWAVE_DIR/${ENV_NODE_FILE}.backup_${backup_suffix}" 2>/dev/null
        fi

        # Restore panel .env if exists
        if [[ -f "$temp_dir/env/$ENV_FILE" ]]; then
            if ! cp "$temp_dir/env/$ENV_FILE" "$REMNAWAVE_DIR/$ENV_FILE" 2>/dev/null; then
                show_warning "Failed to restore panel .env file"
            fi
        fi

        # Restore node .env if exists
        if [[ -f "$temp_dir/env/$ENV_NODE_FILE" ]]; then
            if ! cp "$temp_dir/env/$ENV_NODE_FILE" "$REMNAWAVE_DIR/$ENV_NODE_FILE" 2>/dev/null; then
                show_warning "Failed to restore node .env file"
            fi
        fi
    fi

    # Restart panel services
    show_info "Starting Remnawave services..."
    if ! docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1; then
        handle_backup_error "Failed to start Remnawave services after restoration" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Wait for services to be ready
    show_info "Waiting for services to start..."
    sleep 5

    # Verify services are running
    local services_running=true
    for container in remnawave remnawave-db; do
        if ! docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
            show_warning "Container $container is not running"
            services_running=false
        fi
    done

    if [ "$services_running" = false ]; then
        show_warning "Some services failed to start properly. Check the panel status manually."
    fi

    # Clean up
    cleanup_temp_files "$temp_dir"

    show_success "$(t backup_restore_success)"
    log_backup_operation "INFO" "Backup restoration completed successfully: $(basename "$selected_backup")"
    prompt_for_enter
    return 0
}

# Function to setup automatic backup scheduling
setup_auto_send() {
    # Initialize backup
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system"
        return 1
    fi

    # Load configuration if exists
    load_config

    clear_screen
    draw_section_header "Automatic Backup Configuration"

    # Check current cron status
    local cron_exists=0
    if crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT_PATH"; then
        cron_exists=1
    fi

    if [[ "$cron_exists" -eq 1 ]]; then
        echo -e "${GREEN}$(t backup_cron_enabled)${NC}"
        echo -e "${YELLOW}$(t backup_cron_time): ${CRON_TIMES:-"00 00 * * *"}${NC}"
        echo ""
        echo -e "${GREEN}1.${NC} $(t backup_cron_set)"
        echo -e "${GREEN}2.${NC} $(t backup_cron_disable)"
        echo
        echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
        echo
        echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
        read choice
        echo

        case $choice in
            0) return ;;
            1) setup_cron ;;
            2) disable_cron ;;
            *)
                clear_screen
                echo -e "${BOLD_RED}$(t error_invalid_choice)${NC}"
                sleep 1
                ;;
        esac
    else
        echo -e "${BOLD_RED}$(t backup_cron_disabled)${NC}"
        echo ""
        echo -e "${GREEN}1.${NC} $(t backup_cron_set)"
        echo
        echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
        echo
        echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
        read choice
        echo

        case $choice in
            0) return ;;
            1) setup_cron ;;
            *)
                clear_screen
                echo -e "${BOLD_RED}$(t error_invalid_choice)${NC}"
                sleep 1
                ;;
        esac
    fi
}

# Function to validate cron schedule format
validate_cron_schedule() {
    local cron_schedule="$1"

    # Split cron schedule into fields using read array to avoid glob expansion
    local fields
    IFS=' ' read -ra fields <<< "$cron_schedule"

    # Check if we have exactly 5 fields
    if [[ ${#fields[@]} -ne 5 ]]; then
        echo "$(t backup_cron_error_fields_count)"
        echo "$(t backup_cron_error_received_fields) ${#fields[@]} $(t backup_cron_error_fields_suffix) ${fields[*]}"
        return 1
    fi

    local minute="${fields[0]}"
    local hour="${fields[1]}"
    local day="${fields[2]}"
    local month="${fields[3]}"
    local weekday="${fields[4]}"

    # Validate minute (0-59)
    if ! validate_cron_field "$minute" 0 59 "minute"; then
        return 1
    fi

    # Validate hour (0-23)
    if ! validate_cron_field "$hour" 0 23 "hour"; then
        return 1
    fi

    # Validate day (1-31)
    if ! validate_cron_field "$day" 1 31 "day"; then
        return 1
    fi

    # Validate month (1-12)
    if ! validate_cron_field "$month" 1 12 "month"; then
        return 1
    fi

    # Validate weekday (0-7, where 0 and 7 are Sunday)
    if ! validate_cron_field "$weekday" 0 7 "weekday"; then
        return 1
    fi

    return 0
}

# Function to validate individual cron field
validate_cron_field() {
    local field="$1"
    local min_val="$2"
    local max_val="$3"
    local field_name="$4"

    # Allow * (any value)
    if [[ "$field" == "*" ]]; then
        return 0
    fi

    # Handle ranges (e.g., 1-5)
    if [[ "$field" =~ ^[0-9]+-[0-9]+$ ]]; then
        local start=$(echo "$field" | cut -d'-' -f1)
        local end=$(echo "$field" | cut -d'-' -f2)
        if [[ $start -lt $min_val || $start -gt $max_val || $end -lt $min_val || $end -gt $max_val || $start -gt $end ]]; then
            echo "$(t backup_cron_error_invalid_range) $field_name $(t backup_cron_error_field_suffix) $field $(t backup_cron_error_valid_range) $min_val-$max_val)"
            return 1
        fi
        return 0
    fi

    # Handle step values (e.g., */5, 0-30/5)
    if [[ "$field" =~ / ]]; then
        local base=$(echo "$field" | cut -d'/' -f1)
        local step=$(echo "$field" | cut -d'/' -f2)

        # Validate step is numeric
        if ! [[ "$step" =~ ^[0-9]+$ ]]; then
            echo "$(t backup_cron_error_invalid_step) $field_name $(t backup_cron_error_field_suffix) $step"
            return 1
        fi

        # Validate base part
        if [[ "$base" != "*" ]]; then
            if ! validate_cron_field "$base" "$min_val" "$max_val" "$field_name"; then
                return 1
            fi
        fi
        return 0
    fi

    # Handle comma-separated values (e.g., 1,3,5)
    if [[ "$field" =~ , ]]; then
        IFS=',' read -ra values <<< "$field"
        for value in "${values[@]}"; do
            if ! validate_cron_field "$value" "$min_val" "$max_val" "$field_name"; then
                return 1
            fi
        done
        return 0
    fi

    # Handle single numeric value
    if [[ "$field" =~ ^[0-9]+$ ]]; then
        if [[ $field -lt $min_val || $field -gt $max_val ]]; then
            echo "$(t backup_cron_error_invalid_value) $field_name $(t backup_cron_error_value_suffix) $field $(t backup_cron_error_valid_range) $min_val-$max_val)"
            return 1
        fi
        return 0
    fi

    echo "$(t backup_cron_error_invalid_format) $field_name $(t backup_cron_error_field_suffix) $field"
    return 1
}

# Function to setup cron job
setup_cron() {
    clear_screen
    draw_section_header "Setup Backup Schedule"

    echo "Select backup schedule (UTC+0):"
    echo -e "${GREEN}1.${NC} $(t backup_cron_every_30_minutes)"
    echo -e "${GREEN}2.${NC} $(t backup_cron_every_1_hour)"
    echo -e "${GREEN}3.${NC} $(t backup_cron_every_2_hours)"
    echo -e "${GREEN}4.${NC} $(t backup_cron_every_4_hours)"
    echo -e "${GREEN}5.${NC} $(t backup_cron_every_12_hours)"
    echo -e "${GREEN}6.${NC} $(t backup_cron_daily_midnight)"
    echo -e "${GREEN}7.${NC} $(t backup_cron_daily_noon)"
    echo -e "${GREEN}8.${NC} $(t backup_cron_weekly_sunday)"
    echo -e "${GREEN}9.${NC} $(t backup_cron_monthly_first)"
    echo -e "${GREEN}10.${NC} $(t backup_cron_custom_schedule)"
    echo
    echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
    echo
    echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
    read choice
    echo

    # Validate choice
    if ! [[ "$choice" =~ ^[0-9]+$ ]] || [[ "$choice" -lt 0 ]] || [[ "$choice" -gt 10 ]]; then
        handle_backup_error "$(t error_invalid_choice)"
        return 1
    fi

    # Handle back option
    if [[ "$choice" -eq 0 ]]; then
        return 0
    fi

    case $choice in
        1) CRON_TIMES="*/30 * * * *" ;;
        2) CRON_TIMES="0 * * * *" ;;
        3) CRON_TIMES="0 */2 * * *" ;;
        4) CRON_TIMES="0 */4 * * *" ;;
        5) CRON_TIMES="0 */12 * * *" ;;
        6) CRON_TIMES="0 0 * * *" ;;
        7) CRON_TIMES="0 12 * * *" ;;
        8) CRON_TIMES="0 0 * * 0" ;;
        9) CRON_TIMES="0 0 1 * *" ;;
        10)
            while true; do
                echo
                echo -e "${BOLD_GREEN}$(t backup_cron_custom_title)${NC}"
                echo -e "${ORANGE}$(t backup_cron_custom_format)${NC}"
                echo
                echo -e "${YELLOW}$(t backup_cron_custom_examples)${NC}"
                echo -e "  ${GREEN}30 14 * * *${NC}     - $(t backup_cron_custom_example_daily)"
                echo -e "  ${GREEN}0 */6 * * *${NC}     - $(t backup_cron_custom_example_6hours)"
                echo -e "  ${GREEN}0 2 * * 1${NC}       - $(t backup_cron_custom_example_monday)"
                echo -e "  ${GREEN}15 3 1 * *${NC}      - $(t backup_cron_custom_example_monthly)"
                echo -e "  ${GREEN}0 0 * * 1-5${NC}     - $(t backup_cron_custom_example_weekdays)"
                echo
                echo -e "${ORANGE}$(t backup_cron_custom_field_ranges)${NC}"
                echo -e "  $(t backup_cron_custom_ranges_text)"
                echo
                echo -ne "${BOLD_BLUE_MENU}$(t backup_cron_custom_prompt) ${NC}"
                read -r CRON_TIMES

                if [[ "$CRON_TIMES" == "back" ]]; then
                    return 0
                fi

                # Validate the cron schedule
                if validate_cron_schedule "$CRON_TIMES"; then
                    echo -e "${GREEN}✓ $(t backup_cron_custom_valid) $CRON_TIMES${NC}"
                    break
                else
                    echo -e "${BOLD_RED}$(t backup_cron_custom_invalid)${NC}"
                    echo -e "${BOLD_YELLOW}$(t prompt_enter_to_continue)${NC}"
                    read
                fi
            done
            ;;
    esac

    # Update crontab with error handling
    if ! (crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT_PATH" || true; echo "$CRON_TIMES $BACKUP_SCRIPT_PATH --create --silent") | crontab -; then
        handle_backup_error "Failed to update crontab"
        return 1
    fi

    # Save configuration
    save_config

    show_success "$(t backup_cron_success)"
    log_backup_operation "INFO" "Backup schedule set: $CRON_TIMES"
    prompt_for_enter
}

# Function to disable cron job
disable_cron() {
    # Check if crontab exists and has entries
    local current_crontab=$(crontab -l 2>/dev/null)

    if [[ -z "$current_crontab" ]]; then
        # No crontab exists, nothing to remove
        show_info "No cron jobs found to remove"
    else
        # Filter out backup script entries
        local new_crontab=$(echo "$current_crontab" | grep -v "$BACKUP_SCRIPT_PATH")

        if [[ "$new_crontab" == "$current_crontab" ]]; then
            # No backup entries found
            show_info "No backup cron jobs found to remove"
        else
            # Apply the filtered crontab
            if [[ -z "$new_crontab" ]]; then
                # Remove entire crontab if no entries left
                crontab -r 2>/dev/null || true
            else
                # Update crontab with remaining entries
                echo "$new_crontab" | crontab -
            fi
        fi
    fi

    # Update configuration
    CRON_TIMES=""
    save_config

    show_success "$(t backup_cron_disabled)"
    log_backup_operation "INFO" "Backup schedule disabled"
    prompt_for_enter
}

# Function to create backup in silent mode (for cron)
create_backup_silent() {
    log_backup_operation "INFO" "Starting automated backup creation"

    # Initialize backup
    if ! initialize_backup; then
        log_backup_operation "ERROR" "Failed to initialize backup system"
        return 1
    fi

    # Load configuration if exists
    load_config

    # Try to get database user from Remnawave .env file if not set in config
    if [[ "$DB_USER" == "postgres" && -f "$REMNAWAVE_DIR/.env" ]]; then
        local env_db_user=$(grep "^POSTGRES_USER=" "$REMNAWAVE_DIR/.env" | cut -d'=' -f2 | tr -d '"')
        if [[ -n "$env_db_user" ]]; then
            DB_USER="$env_db_user"
            log_backup_operation "INFO" "Using database user from .env: $DB_USER"
        fi
    fi

    # Generate timestamp for backup file
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="remnawave_backup_${timestamp}.tar.gz"
    local backup_path="$BACKUP_DIR/$backup_file"

    # Create temporary directory
    local temp_dir=$(mktemp -d)
    local db_dump="$temp_dir/database.sql"
    local env_dir="$temp_dir/env"
    local checksum_file="$temp_dir/$BACKUP_CHECKSUM_FILE"

    # Create directory for env files
    if ! mkdir -p "$env_dir"; then
        log_backup_operation "ERROR" "Failed to create temporary environment directory"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Check if remnawave-db container exists and is running
    if ! docker ps --format "{{.Names}}" | grep -q "^remnawave-db$"; then
        log_backup_operation "ERROR" "Remnawave database container is not running"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Export PostgreSQL database from Docker container
    if ! docker exec remnawave-db pg_dump -U "$DB_USER" -d remnawave_db > "$db_dump" 2>/dev/null; then
        log_backup_operation "ERROR" "Failed to export database. Check if database user '$DB_USER' exists"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Verify database dump is not empty
    if [[ ! -s "$db_dump" ]]; then
        log_backup_operation "ERROR" "Database dump is empty"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Find and copy .env files
    # Check for panel .env file and copy if exists
    if [[ -f "$REMNAWAVE_DIR/$ENV_FILE" ]]; then
        if ! cp "$REMNAWAVE_DIR/$ENV_FILE" "$env_dir/" 2>/dev/null; then
            log_backup_operation "WARNING" "Failed to copy panel .env file"
        fi
    fi

    # Check for node .env file and copy if exists
    if [[ -f "$REMNAWAVE_DIR/$ENV_NODE_FILE" ]]; then
        if ! cp "$REMNAWAVE_DIR/$ENV_NODE_FILE" "$env_dir/" 2>/dev/null; then
            log_backup_operation "WARNING" "Failed to copy node .env file"
        fi
    fi

    # Create backup metadata
    echo "$BACKUP_MAGIC_HEADER" > "$temp_dir/backup.info"
    echo "Created: $(date '+%Y-%m-%d %H:%M:%S')" >> "$temp_dir/backup.info"
    echo "Server: $(hostname)" >> "$temp_dir/backup.info"
    echo "Version: $VERSION" >> "$temp_dir/backup.info"

    # Generate checksums for validation
    cd "$temp_dir"
    find . -type f -not -name "$BACKUP_CHECKSUM_FILE" -exec sha256sum {} \; > "$checksum_file"
    cd - >/dev/null

    # Create archive of all backup files
    if ! tar -czf "$backup_path" -C "$temp_dir" . >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Failed to create backup archive"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Validate the created backup
    if ! validate_backup_integrity "$backup_path" "$temp_dir"; then
        log_backup_operation "ERROR" "Backup validation failed"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Clean up temporary directory
    cleanup_temp_files "$temp_dir"

    # Clean up old backups (older than specified days)
    find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -mtime +"$RETAIN_BACKUPS_DAYS" -delete 2>/dev/null

    # Send backup to the configured method if available
    if [[ "$UPLOAD_METHOD" == "telegram" && -n "$BOT_TOKEN" && -n "$CHAT_ID" ]]; then
        local server_name=$(hostname)
        local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")
        local caption="Remnawave Backup\nServer: $server_name\nIP: $server_ip\nDate: $(date '+%Y-%m-%d %H:%M:%S')"

        if send_telegram_document "$backup_path" "$caption"; then
            log_backup_operation "INFO" "Backup sent via Telegram successfully"
        else
            log_backup_operation "ERROR" "Failed to send backup via Telegram"
        fi
    elif [[ "$UPLOAD_METHOD" == "gdrive" && -n "$GD_CLIENT_ID" && -n "$GD_CLIENT_SECRET" ]]; then
        log_backup_operation "INFO" "Google Drive upload not yet implemented"
    fi

    log_backup_operation "INFO" "Backup created successfully: $backup_path"
    return 0
}

# Main execution logic for when script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # If --create flag is provided, run backup creation
    if [[ "$BACKUP_CREATE_MODE" == true ]]; then
        if [[ "$BACKUP_SILENT_MODE" == true ]]; then
            # Silent mode for cron - no interactive output
            create_backup_silent
        else
            # Interactive mode
            create_backup
        fi
        exit $?
    else
        # No flags provided, show interactive menu
        backup_restore_menu
    fi
fi

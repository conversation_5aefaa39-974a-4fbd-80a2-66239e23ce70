#!/bin/bash

# Test script to verify backup CLI argument handling

echo "=== Testing Backup CLI Argument Handling ==="
echo

# Test 1: Check syntax
echo "Test 1: Verify backup module syntax"
echo "-----------------------------------"
if bash -n src/modules/tools/backup-restore.sh; then
    echo "✓ Backup module syntax is valid"
else
    echo "✗ Backup module has syntax errors"
    exit 1
fi

# Test 2: Check CLI argument parsing
echo
echo "Test 2: Check CLI argument parsing"
echo "----------------------------------"
if grep -q "BACKUP_CREATE_MODE=false" src/modules/tools/backup-restore.sh; then
    echo "✓ BACKUP_CREATE_MODE variable found"
else
    echo "✗ BACKUP_CREATE_MODE variable missing"
fi

if grep -q "BACKUP_SILENT_MODE=false" src/modules/tools/backup-restore.sh; then
    echo "✓ BACKUP_SILENT_MODE variable found"
else
    echo "✗ BACKUP_SILENT_MODE variable missing"
fi

if grep -q "\-\-create)" src/modules/tools/backup-restore.sh; then
    echo "✓ --create argument handling found"
else
    echo "✗ --create argument handling missing"
fi

if grep -q "\-\-silent)" src/modules/tools/backup-restore.sh; then
    echo "✓ --silent argument handling found"
else
    echo "✗ --silent argument handling missing"
fi

# Test 3: Check silent backup function
echo
echo "Test 3: Check silent backup function"
echo "-------------------------------------"
if grep -q "create_backup_silent()" src/modules/tools/backup-restore.sh; then
    echo "✓ create_backup_silent function found"
else
    echo "✗ create_backup_silent function missing"
fi

# Test 4: Check main execution logic
echo
echo "Test 4: Check main execution logic"
echo "-----------------------------------"
if grep -q "BACKUP_CREATE_MODE.*true" src/modules/tools/backup-restore.sh; then
    echo "✓ CREATE_MODE execution logic found"
else
    echo "✗ CREATE_MODE execution logic missing"
fi

if grep -q "BACKUP_SILENT_MODE.*true" src/modules/tools/backup-restore.sh; then
    echo "✓ SILENT_MODE execution logic found"
else
    echo "✗ SILENT_MODE execution logic missing"
fi

# Test 5: Check BASH_SOURCE condition
echo
echo "Test 5: Check script execution detection"
echo "-----------------------------------------"
if grep -q 'BASH_SOURCE\[0\].*==.*0' src/modules/tools/backup-restore.sh; then
    echo "✓ Script execution detection found"
else
    echo "✗ Script execution detection missing"
fi

# Test 6: Check built installer
echo
echo "Test 6: Check built installer"
echo "------------------------------"
if grep -q "BACKUP_CREATE_MODE=false" dist/install_remnawave.sh; then
    echo "✓ CLI argument handling found in built installer"
else
    echo "✗ CLI argument handling missing in built installer"
fi

if grep -q "create_backup_silent()" dist/install_remnawave.sh; then
    echo "✓ Silent backup function found in built installer"
else
    echo "✗ Silent backup function missing in built installer"
fi

# Test 7: Test argument parsing logic (simulation)
echo
echo "Test 7: Test argument parsing simulation"
echo "----------------------------------------"

# Create a test script to simulate argument parsing
cat > test_args_simulation.sh << 'EOF'
#!/bin/bash

# Simulate the argument parsing logic
BACKUP_CREATE_MODE=false
BACKUP_SILENT_MODE=false

# Test arguments
test_args=("--create" "--silent")

for arg in "${test_args[@]}"; do
    case $arg in
        --create)
            BACKUP_CREATE_MODE=true
            ;;
        --silent)
            BACKUP_SILENT_MODE=true
            ;;
    esac
done

echo "CREATE_MODE: $BACKUP_CREATE_MODE"
echo "SILENT_MODE: $BACKUP_SILENT_MODE"

if [[ "$BACKUP_CREATE_MODE" == true && "$BACKUP_SILENT_MODE" == true ]]; then
    echo "✓ Argument parsing logic works correctly"
    exit 0
else
    echo "✗ Argument parsing logic failed"
    exit 1
fi
EOF

chmod +x test_args_simulation.sh
if ./test_args_simulation.sh; then
    echo "✓ Argument parsing simulation passed"
else
    echo "✗ Argument parsing simulation failed"
fi

# Clean up
rm -f test_args_simulation.sh

echo
echo "=== Test completed ==="

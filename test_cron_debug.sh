#!/bin/bash

# Debug script to check cron configuration

echo "=== Debugging Cron Configuration ==="
echo

echo "1. Current crontab entries:"
echo "----------------------------"
crontab -l 2>/dev/null || echo "No crontab found"

echo
echo "2. Check if backup script exists:"
echo "---------------------------------"
BACKUP_SCRIPT_PATH="/opt/rw-backup-restore/rw-backup.sh"
if [[ -f "$BACKUP_SCRIPT_PATH" ]]; then
    echo "✓ Backup script exists: $BACKUP_SCRIPT_PATH"
    echo "Script permissions: $(ls -la "$BACKUP_SCRIPT_PATH")"
else
    echo "✗ Backup script not found: $BACKUP_SCRIPT_PATH"
fi

echo
echo "3. Check backup directory:"
echo "--------------------------"
BACKUP_DIR="/opt/rw-backup-restore"
if [[ -d "$BACKUP_DIR" ]]; then
    echo "✓ Backup directory exists: $BACKUP_DIR"
    echo "Directory contents:"
    ls -la "$BACKUP_DIR"
else
    echo "✗ Backup directory not found: $BACKUP_DIR"
fi

echo
echo "4. Check backup configuration:"
echo "------------------------------"
CONFIG_FILE="/opt/rw-backup-restore/config.env"
if [[ -f "$CONFIG_FILE" ]]; then
    echo "✓ Config file exists: $CONFIG_FILE"
    echo "Config contents:"
    cat "$CONFIG_FILE"
else
    echo "✗ Config file not found: $CONFIG_FILE"
fi

echo
echo "5. Test backup script execution:"
echo "--------------------------------"
if [[ -f "$BACKUP_SCRIPT_PATH" ]]; then
    echo "Testing script syntax:"
    if bash -n "$BACKUP_SCRIPT_PATH"; then
        echo "✓ Script syntax is valid"
    else
        echo "✗ Script has syntax errors"
    fi
    
    echo
    echo "Testing --help or no arguments:"
    timeout 10s bash "$BACKUP_SCRIPT_PATH" --help 2>/dev/null || echo "Script executed (timeout after 10s)"
else
    echo "Cannot test - script not found"
fi

echo
echo "6. Check recent backup logs:"
echo "----------------------------"
LOG_FILE="/opt/rw-backup-restore/backup.log"
if [[ -f "$LOG_FILE" ]]; then
    echo "✓ Log file exists: $LOG_FILE"
    echo "Recent log entries (last 10 lines):"
    tail -10 "$LOG_FILE"
else
    echo "✗ Log file not found: $LOG_FILE"
fi

echo
echo "7. Check system cron service:"
echo "-----------------------------"
if systemctl is-active --quiet cron; then
    echo "✓ Cron service is running"
elif systemctl is-active --quiet crond; then
    echo "✓ Crond service is running"
else
    echo "✗ Cron service is not running"
    echo "Cron service status:"
    systemctl status cron 2>/dev/null || systemctl status crond 2>/dev/null || echo "Cron service not found"
fi

echo
echo "8. Check cron logs (if available):"
echo "-----------------------------------"
if [[ -f /var/log/cron ]]; then
    echo "Recent cron log entries:"
    tail -5 /var/log/cron | grep -i backup || echo "No backup-related entries found"
elif [[ -f /var/log/syslog ]]; then
    echo "Recent syslog cron entries:"
    tail -20 /var/log/syslog | grep -i cron | grep -i backup || echo "No backup-related cron entries found"
else
    echo "No cron logs found"
fi

echo
echo "=== Debug completed ==="
